# GTK Color Sourcing for HyprMenu

## Overview

HyprMenu now supports sourcing colors from your GTK CSS file instead of using hardcoded colors. This allows HyprMenu to automatically match your system's GTK theme colors.

## How It Works

When enabled, HyprMenu will read your GTK CSS file and extract colors from common selectors to apply to various HyprMenu elements:

- **Window background**: From `window` selector or `@define-color theme_bg_color`
- **Text colors**: From `window` selector or `@define-color theme_fg_color` 
- **Hover/active colors**: From `:selected` selector or `@define-color theme_selected_bg_color`
- **Border colors**: From `entry` selector or `@define-color borders`

## Configuration

The new color sourcing feature is controlled by the `[ColorSource]` section in your HyprMenu configuration file (`~/.config/hyprmenu/hyprmenu.conf`):

```ini
[ColorSource]
# Color sourcing settings
use_gtk_colors=true                                    # Enable sourcing colors from GTK CSS file
gtk_css_path=/home/<USER>/.config/gtk-4.0/gtk.css     # Path to GTK CSS file for color sourcing
auto_reload_colors=true                               # Automatically reload colors when GTK CSS file changes
```

## How to Enable

1. **Edit your HyprMenu configuration file**:
   ```bash
   nano ~/.config/hyprmenu/hyprmenu.conf
   ```

2. **Find the `[ColorSource]` section** and set:
   ```ini
   use_gtk_colors=true
   ```

3. **Verify the GTK CSS path** is correct for your system:
   ```ini
   gtk_css_path=/home/<USER>/.config/gtk-4.0/gtk.css
   ```

4. **Save the file and restart HyprMenu**

## What Happens

When `use_gtk_colors=true`:

1. HyprMenu will read your GTK CSS file on startup
2. It will extract colors from common GTK selectors
3. These colors will override the default HyprMenu colors
4. The configuration file will NOT be modified - colors are applied at runtime
5. You'll see debug messages in the console showing which colors were loaded

## Benefits

- **Automatic theme matching**: HyprMenu will automatically match your GTK theme
- **No manual color configuration**: No need to manually set each color in the config
- **Dynamic updates**: Change your GTK theme and HyprMenu will pick up the new colors
- **Preserves manual overrides**: You can still manually override specific colors in the config

## Troubleshooting

- **Colors not loading**: Check that the GTK CSS file path is correct
- **Partial colors**: Some GTK themes may not define all color variables - HyprMenu will fall back to defaults
- **Debug output**: Run HyprMenu from terminal to see color loading messages

## Supported Color Variables

HyprMenu looks for these `@define-color` variables in your GTK CSS file:

**Window/Background Colors:**
- `window_bg_color`, `theme_bg_color`, `bg_color`
- `view_bg_color`, `card_bg_color` (for search background)
- `sidebar_bg_color`, `headerbar_bg_color` (for categories)

**Text Colors:**
- `window_fg_color`, `theme_fg_color`, `fg_color`

**Accent/Hover Colors:**
- `accent_color`, `accent_bg_color`, `theme_selected_bg_color`

**Border Colors:**
- `borders`, `border_color`, `headerbar_border_color`

## Example

With GTK color sourcing enabled, if your GTK theme has:
```css
@define-color accent_color #adc8ff;
@define-color accent_bg_color #4c6ff;
@define-color window_bg_color #1e1e2e;
@define-color window_fg_color #cdd6f4;
@define-color sidebar_bg_color #313244;
```

HyprMenu will automatically use:
- `#1e1e2e` for window background
- `#cdd6f4` for text colors
- `#adc8ff` for hover/active states
- `#313244` for category backgrounds

## Disabling

To disable GTK color sourcing, set:
```ini
use_gtk_colors=false
```

HyprMenu will then use the colors defined in the configuration file or fall back to defaults.
