#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <glib.h>

// Simple version of the color extraction function for testing
gchar* extract_define_color(const gchar *css_content, const gchar *color_name)
{
  if (!css_content || !color_name) {
    return NULL;
  }
  
  // Look for @define-color color_name pattern
  gchar *pattern = g_strdup_printf("@define-color %s", color_name);
  gchar *define_pos = g_strstr_len(css_content, -1, pattern);
  g_free(pattern);
  
  if (!define_pos) {
    return NULL;
  }
  
  // Skip past the color name to find the color value
  gchar *value_start = define_pos + strlen("@define-color ") + strlen(color_name);
  
  // Skip whitespace
  while (*value_start && g_ascii_isspace(*value_start)) {
    value_start++;
  }
  
  // Find the end of the value (semicolon or newline)
  gchar *value_end = value_start;
  while (*value_end && *value_end != ';' && *value_end != '\n' && *value_end != '\r') {
    value_end++;
  }
  
  // Trim trailing whitespace
  while (value_end > value_start && g_ascii_isspace(*(value_end - 1))) {
    value_end--;
  }
  
  if (value_end <= value_start) {
    return NULL;
  }
  
  return g_strndup(value_start, value_end - value_start);
}

int main()
{
  // Read your GTK CSS file
  gchar *css_content = NULL;
  GError *error = NULL;
  gchar *gtk_css_path = g_build_filename(g_get_home_dir(), ".config", "gtk-4.0", "gtk.css", NULL);
  
  if (!g_file_get_contents(gtk_css_path, &css_content, NULL, &error)) {
    printf("Failed to read GTK CSS file: %s\n", error->message);
    g_error_free(error);
    g_free(gtk_css_path);
    return 1;
  }
  
  printf("Reading colors from: %s\n\n", gtk_css_path);
  
  // Test color extraction for various color names
  const gchar *color_names[] = {
    "accent_color",
    "accent_bg_color", 
    "accent_fg_color",
    "window_bg_color",
    "window_fg_color",
    "view_bg_color",
    "view_fg_color",
    "headerbar_bg_color",
    "headerbar_fg_color",
    "sidebar_bg_color",
    "sidebar_fg_color",
    "card_bg_color",
    "card_fg_color",
    "popover_bg_color",
    "popover_fg_color",
    "borders",
    "theme_bg_color",
    "theme_fg_color",
    "theme_selected_bg_color",
    NULL
  };
  
  printf("Extracted colors:\n");
  printf("=================\n");
  
  for (int i = 0; color_names[i] != NULL; i++) {
    gchar *color_value = extract_define_color(css_content, color_names[i]);
    if (color_value) {
      printf("%-25s: %s\n", color_names[i], color_value);
      g_free(color_value);
    }
  }
  
  g_free(css_content);
  g_free(gtk_css_path);
  return 0;
}
