# HyprMenu Configuration File
# This file controls the appearance and behavior of HyprMenu
# All color values support #RRGGBB, #RRGGBBAA, rgb(), or rgba() formats

[Layout]
# Main window dimensions and positioning
window_width=800        # Width of the menu window in pixels
window_height=600       # Height of the menu window in pixels
top_margin=48          # Margin from the top of the screen
left_margin=8          # Margin from the left of the screen
center_window=false    # Whether to center the window on screen
bottom_offset=55       # Offset from bottom for dock/panel (0 to respect reserved space)
top_offset=48          # Offset from top for panel
window_padding=8       # Internal padding of the main window

[Window]
# Main window appearance
background_color=      # Window background color (empty for transparent)
background_opacity=1.0 # Overall window opacity (0.0 to 1.0)
background_blur=5.0    # Background blur strength
corner_radius=12       # Window corner radius
halign=center         # Horizontal alignment (start, center, end)
valign=center         # Vertical alignment (start, center, end)
shadow_color=rgba(0,0,0,0.3)  # Window shadow color
shadow_radius=20      # Window shadow radius
opacity=1.0           # Overall window opacity

[Border]
# Border appearance settings
inner_border_color=#444444    # Color of the inner border
inner_border_radius=12        # Radius of inner border corners
inner_border_width=2          # Width of inner border
outer_border_color=#888888    # Color of the outer border
outer_border_radius=16        # Radius of outer border corners
outer_border_width=3          # Width of outer border

[Grid]
# Grid view settings (when showing apps in grid mode)
columns=5                     # Number of columns in grid
item_size=100                # Size of each grid item
item_corner_radius=8         # Corner radius of grid items
item_border_width=1          # Border width of grid items
item_border_color=rgba(255,255,255,0.08)  # Border color of grid items
item_background_color=rgba(50,50,60,0.7)  # Background color of grid items
row_spacing=12               # Vertical spacing between rows
column_spacing=12            # Horizontal spacing between columns
margin_start=12              # Left margin
margin_end=12                # Right margin
margin_top=12                # Top margin
margin_bottom=12             # Bottom margin
halign=center                # Horizontal alignment of grid
valign=center                # Vertical alignment of grid
hexpand=true                 # Whether grid expands horizontally
vexpand=false                # Whether grid expands vertically
opacity=1.0                  # Overall grid opacity
item_opacity=1.0             # Individual item opacity

[List]
# List view settings (when showing apps in list mode)
item_size=48                 # Height of each list item
item_corner_radius=6         # Corner radius of list items
item_border_width=1          # Border width of list items
item_border_color=rgba(255,255,255,0.05)  # Border color of list items
item_background_color=rgba(60,60,70,0.6)  # Background color of list items
row_spacing=8                # Vertical spacing between items
margin_start=12              # Left margin
margin_end=12                # Right margin
margin_top=12                # Top margin
margin_bottom=12             # Bottom margin
halign=fill                  # Horizontal alignment of list
valign=center                # Vertical alignment of list
hexpand=true                 # Whether list expands horizontally
vexpand=false                # Whether list expands vertically
opacity=1.0                  # Overall list opacity
item_opacity=1.0             # Individual item opacity

[AppEntry]
# Individual application entry appearance
icon_size=32                 # Size of application icons
icon_corner_radius=6         # Corner radius of icons
icon_background_color=rgba(60,60,70,0.6)  # Background color behind icons
name_font_size=12            # Font size of application names
name_color=#ffffff           # Color of application names
desc_font_size=10            # Font size of application descriptions
desc_color=rgba(255,255,255,0.7)  # Color of application descriptions
padding=6                    # Internal padding of entries
hover_color=rgba(100,100,100,0.8)  # Background color on hover
active_color=rgba(100,100,100,0.9)  # Background color when clicked
opacity=1.0                  # Overall entry opacity
icon_opacity=1.0             # Icon opacity
name_opacity=1.0             # Name text opacity
desc_opacity=1.0             # Description text opacity

[Category]
# Category header appearance
background_color=#2d2d2d     # Category background color
background_opacity=1.0       # Category background opacity
corner_radius=10             # Corner radius of category headers
text_color=rgba(255,255,255,0.9)  # Category text color
font_size=13                 # Category text size
font_family=Sans Bold        # Category font family
padding=6                    # Internal padding of categories
show_separators=true         # Whether to show separators between categories
separator_color=rgba(255,255,255,0.1)  # Color of category separators
opacity=1.0                  # Overall category opacity
title_opacity=1.0            # Category title opacity

[Search]
# Search bar appearance and behavior
background_color=rgba(34,34,34,0.3)  # Search bar background color
background_opacity=1.0       # Search bar background opacity
corner_radius=8              # Corner radius of search bar
text_color=#ffffff           # Search text color
font_size=14                 # Search text size
font_family=Sans             # Search text font
padding=8                    # Internal padding of search bar
min_height=20                # Minimum height of search bar
left_padding=2               # Left padding of search text
length=0                     # Maximum search text length (0 for unlimited)
placeholder_text=Search apps...  # Placeholder text when empty
icon_size=18                 # Size of search icon
icon_color=#ffffff           # Color of search icon
focus_border_color=rgba(255,255,255,0.5)  # Border color when focused
focus_shadow_color=rgba(255,255,255,0.2)  # Shadow color when focused
opacity=1.0                  # Overall search bar opacity
text_opacity=1.0             # Search text opacity
icon_opacity=1.0             # Search icon opacity

[SystemButton]
# System button appearance (power, settings, etc.)
background_color=rgba(40,42,54,0.7)  # Button background color
icon_color=rgba(255,255,255,0.85)   # Button icon color
hover_color=rgba(80,85,100,0.5)     # Background color on hover
active_color=rgba(90,95,120,0.6)    # Background color when clicked
corner_radius=6              # Corner radius of buttons
size=24                      # Size of buttons
spacing=8                    # Space between buttons
opacity=1.0                  # Overall button opacity
icon_opacity=1.0             # Button icon opacity

[Behavior]
# Program behavior settings
close_on_click_outside=true  # Close when clicking outside the menu
close_on_super_key=true      # Close when pressing Super key
close_on_app_launch=true     # Close when launching an application
focus_search_on_open=true    # Focus search bar when opening
close_on_escape=true         # Close when pressing Escape
close_on_focus_out=true      # Close when losing focus
show_categories=true         # Show application categories
show_descriptions=true       # Show application descriptions
show_icons=true             # Show application icons
show_search=true            # Show search bar
show_scrollbar=true         # Show scrollbar
show_border=true            # Show window border
show_shadow=true            # Show window shadow
blur_background=true        # Enable background blur
blur_strength=5             # Background blur strength
max_recent_apps=5           # Maximum number of recent apps to show

[Transparency]
# Global transparency settings
enabled=true                # Enable transparency effects
alpha=1.0                   # Global alpha value (0.0 to 1.0)
blur=true                   # Enable blur effects
shadow=true                 # Enable shadow effects
shadow_color=rgba(0,0,0,0.3)  # Shadow color
shadow_radius=20            # Shadow radius