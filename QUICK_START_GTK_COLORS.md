# Quick Start: GTK Color Sourcing

## Problem Solved
HyprMenu was formatting your configuration file with hardcoded colors every time it launched. Now it can source colors dynamically from your GTK CSS file instead.

## Quick Setup

1. **Enable GTK color sourcing** in your HyprMenu config:
   ```bash
   nano ~/.config/hyprmenu/hyprmenu.conf
   ```

2. **Find the `[ColorSource]` section** and change:
   ```ini
   [ColorSource]
   use_gtk_colors=true
   gtk_css_path=/home/<USER>/.config/gtk-4.0/gtk.css
   auto_reload_colors=true
   ```

3. **Save and restart HyprMenu**

## What Happens Now

✅ **No more config formatting** - HyprMenu won't overwrite your configuration  
✅ **Dynamic colors** - Colors are loaded from your GTK CSS file at runtime  
✅ **Automatic updates** - When your GTK theme changes, HyprMenu picks up the new colors  
✅ **Preserves manual settings** - You can still override specific colors in the config  

## Test It

Run this command to see what colors HyprMenu extracts from your GTK CSS:
```bash
./test_color_extraction
```

## Your GTK CSS File

Your file at `/home/<USER>/.config/gtk-4.0/gtk.css` contains `@define-color` variables like:
- `accent_color`
- `window_bg_color` 
- `sidebar_bg_color`
- etc.

HyprMenu now reads these dynamically instead of using hardcoded values.

## Disable If Needed

To go back to the old behavior:
```ini
use_gtk_colors=false
```

That's it! Your HyprMenu will now match your GTK theme automatically and won't format your configuration file anymore.
